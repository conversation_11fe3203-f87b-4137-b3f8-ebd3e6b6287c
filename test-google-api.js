// Test script to verify Google Places API key
// Run this with: node test-google-api.js

const https = require('https');

// Replace with your actual API key
const API_KEY = 'AIzaSyA0NIYllt6HgBKqD0QeE0Oa5d3ZbysGPSE';

// Test the Places API with a simple query
const testQuery = 'restaurant';
const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${testQuery}&key=${API_KEY}`;

console.log('Testing Google Places API...');
console.log('API Key:', API_KEY);
console.log('Test URL:', url);
console.log('---');

https.get(url, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('Response Status:', response.status);
      
      if (response.status === 'OK') {
        console.log('✅ API Key is working!');
        console.log('Number of predictions:', response.predictions?.length || 0);
        if (response.predictions && response.predictions.length > 0) {
          console.log('First prediction:', response.predictions[0].description);
        }
      } else {
        console.log('❌ API Error:', response.status);
        console.log('Error message:', response.error_message || 'No error message');
        
        // Common error explanations
        switch (response.status) {
          case 'REQUEST_DENIED':
            console.log('💡 This usually means:');
            console.log('   - API key is invalid');
            console.log('   - Places API is not enabled for this key');
            console.log('   - API key restrictions are blocking the request');
            break;
          case 'OVER_QUERY_LIMIT':
            console.log('💡 You have exceeded your quota for this API');
            break;
          case 'INVALID_REQUEST':
            console.log('💡 The request is missing required parameters');
            break;
        }
      }
      
      console.log('\nFull response:');
      console.log(JSON.stringify(response, null, 2));
      
    } catch (error) {
      console.log('❌ Failed to parse response:', error.message);
      console.log('Raw response:', data);
    }
  });

}).on('error', (err) => {
  console.log('❌ Network error:', err.message);
});
