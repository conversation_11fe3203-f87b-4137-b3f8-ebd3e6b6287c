import Loader from '@app/components/Loader';
import AppNavigation from '@app/navigation/AppNavigation';
import React, {useEffect, useState} from 'react';
import 'react-native-gesture-handler';
import FlashMessage from 'react-native-flash-message';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {Alert, Linking, LogBox, Platform, StatusBar} from 'react-native';
import {StripeProvider} from '@stripe/stripe-react-native';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {COLORS} from '@app/constants/styles';
import Config from 'react-native-config';
import * as amplitude from '@amplitude/analytics-react-native';
import {LogLevel, initialize} from 'react-native-clarity';
import VersionCheck from 'react-native-version-check';
import {Settings} from 'react-native-fbsdk-next';
import AppUpdateModal from '@app/components/AppUpdateModal';
import SplashScreen from '@app/screens/splash/SplashScreen';
import {PERMISSIONS, request} from 'react-native-permissions';
import useGlobalErrorHandler from '@app/hooks/useGlobalErrorHandler';
import { getMessaging, requestPermission ,AuthorizationStatus, getToken} from '@react-native-firebase/messaging';
import NativeDevSettings from 'react-native/Libraries/NativeModules/specs/NativeDevSettings';

const App: React.FC = () => {
  const [isUpdateModalVisible, setUpdateModalVisible] = useState(false);
  const [storeUrl, setStoreUrl] = useState('');
  const [isSplashVisible, setSplashVisible] = useState(true); 
  const connectToRemoteDebugger = () => {
    NativeDevSettings.setIsDebuggingRemotely(true);
  };
  // useGlobalErrorHandler(); // Used for global error handling
  Settings.initializeSDK();
  const messaging = getMessaging();
  useEffect(() => {
    VersionCheck.needUpdate().then(async res => {
      // Alert.alert('kkk',JSON.stringify(res))
      if (res?.isNeeded) {
        setUpdateModalVisible(true);
        setStoreUrl(res.storeUrl);
      }
    });
  }, []);

  const appUpdate = () => {
    setUpdateModalVisible(false);
    Linking.openURL(storeUrl);
  };

  const STRIPE_PK_KEY_DEV = Config.STRIPE_PK_KEY || '';
  const clarityConfig = {
  logLevel: LogLevel.Verbose,
  };

  useEffect(() => {
    initialize('clarityConfig', clarityConfig);
    GoogleSignin.configure({
      webClientId:
        '671879550955-l38da4mi07jknaape45g6tcg8rv4jekv.apps.googleusercontent.com',
     iosClientId: '671879550955-4rvulmne36i9lhpojttoct2pg7he27p3.apps.googleusercontent.com',
      });

    if (Platform.OS === 'ios') {
      requestNotificationPermission();
    }
    if (Platform.OS == 'android') {
      requestNotificationPermissionAnd();
    }
    amplitude.init('365fd6235effb76b8f805cd428798665');
  }, []);

  useEffect(() => {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.checkPermissions(perms => {});
    }
  }, []);

  useEffect(() => {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.addEventListener(
        'notification',
        onRemoteNotification,
      );
    }
  }, []);

  const onRemoteNotification = (notification: any) => {
    const isClicked = notification.getData().userInteraction === 1;
    console.log(isClicked);
  };

  const requestNotificationPermission = async () => {
    const authStatus = await requestPermission(messaging);
    const enabled =
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL;
    if (enabled) {
      await generateToken();
    }
  };
  const requestNotificationPermissionAnd = async () => {
    const result = await request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
    if (result == 'granted') {
      await generateToken();
    }
    return result;
  };
  const generateToken = async () => {
    let token = '';
    if (!token) {
      token = await getToken(messaging);
      console.log('[FCM TOKEN]', token);
    }
  };

  // Function to handle splash screen transition
  useEffect(() => {
    const timer = setTimeout(() => {
      setSplashVisible(false);
    }, 3000); // Adjust this timeout as necessary

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <StatusBar backgroundColor={COLORS.WHITE} barStyle={'dark-content'} />
      <AppUpdateModal visible={isUpdateModalVisible} onCheers={appUpdate} />
      <StripeProvider
        publishableKey={STRIPE_PK_KEY_DEV}
        merchantIdentifier="merchant.com.winelikes">
        {isSplashVisible ? <SplashScreen /> : <AppNavigation />}
        <Loader />
        <FlashMessage position="top" />
      </StripeProvider>
    </>
  );
};

export default App;

// if (!__DEV__) {
//   console.log = () => {};
// }

// Temporarily enable logs for debugging Google Places
// LogBox.ignoreAllLogs(); //Ignore all log notifications
LogBox.ignoreLogs(['VirtualizedLists should never be nested']);
